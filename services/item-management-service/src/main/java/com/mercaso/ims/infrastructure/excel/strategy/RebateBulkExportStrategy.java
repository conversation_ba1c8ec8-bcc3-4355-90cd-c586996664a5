package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.query.ItemVendorRebateQuery;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.domain.itemsalestrend.service.ItemSalesTrendService;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.excel.data.RebateReportData;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_LIST_SEARCH_JSON_PROCESSING_EXCEPTION;

@Component
@Slf4j
public class RebateBulkExportStrategy extends AbstractBulkExportStrategy {

    private final BulkExportExcelGenerator excelGenerator;
    private final ItemSalesTrendService itemSalesTrendService;
    private final VendorService vendorService;
    private final ItemRepository itemRepository;

    public RebateBulkExportStrategy(ItemQueryApplicationService itemQueryApplicationService, Executor taskExecutor,
                                    ItemSearchApplicationService itemSearchApplicationService,
                                    BulkExportExcelGenerator excelGenerator,
                                    ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService,
                                    ItemSalesTrendService itemSalesTrendService,
                                    VendorService vendorService,
                                    ItemRepository itemRepository) {
        super(itemQueryApplicationService, taskExecutor, itemSearchApplicationService, itemVendorRebateSearchApplicationService);
        this.excelGenerator = excelGenerator;
        this.itemSalesTrendService = itemSalesTrendService;
        this.vendorService = vendorService;
        this.itemRepository = itemRepository;
    }

    @Override
    public ExportType getExportType() {
        return ExportType.REBATE;
    }

    @Override
    public byte[] execute(String customFilter) {
        log.info("Executing rebate export with customFilter: {}", customFilter);

        // Parse the custom filter to get rebate export parameters
        ItemVendorRebateQuery itemVendorRebateQuery =
                ItemVendorRebateQuery.builder()
                        .customFilter(ItemVendorRebateQuery.getCustomFilterValue(customFilter))
                        .build();

        // Validate the filter
        if (!itemVendorRebateQuery.isValid()) {
            throw new ImsBusinessException(ITEM_LIST_SEARCH_JSON_PROCESSING_EXCEPTION.getCode(),
                    itemVendorRebateQuery.getValidationError());
        }

        List<RebateReportData> rebateData = calculateRebateReport(itemVendorRebateQuery, customFilter);

        // Generate Excel using the specialized rebate generator
        return excelGenerator.generateRebateExportReport(rebateData);
    }

    public List<RebateReportData> calculateRebateReport(ItemVendorRebateQuery itemVendorRebateQuery, String customFilter) {
        UUID vendorId = itemVendorRebateQuery.getVendorId();
        LocalDate startDate = itemVendorRebateQuery.getStartDate();
        LocalDate endDate = itemVendorRebateQuery.getEndDate();
        log.info("Calculating rebate report for vendor: {} from {} to {}", vendorId, startDate, endDate);

        // Fetch rebate rules
        List<ItemVendorRebateDto> itemVendorRebateDtos = fetchItemVendorRebateData(customFilter);
        log.info("Generated {} rebate report records", itemVendorRebateDtos.size());

        if (itemVendorRebateDtos.isEmpty()) {
            log.info("No rebate rules found for vendor: {} in date range", vendorId);
            return Collections.emptyList();
        }

        // Get vendor information
        Vendor vendor = vendorService.findById(vendorId);
        if (vendor == null) {
            log.warn("Vendor not found: {}", vendorId);
            return Collections.emptyList();
        }

        // Get item IDs from rebate rules
        Set<String> itemIds = itemVendorRebateDtos.stream()
                .map(rebate -> rebate.getItemId().toString())
                .collect(Collectors.toSet());

        // Fetch sales data for the date range
        Date startDateAsDate = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateAsDate = Date.from(endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());

        List<ItemSalesTrend> salesData = itemSalesTrendService.findByItemIdsAndTimeDimBetween(
                new ArrayList<>(itemIds), startDateAsDate, endDateAsDate, ItemSalesTrendTimeGrain.DAY);

        if (salesData.isEmpty()) {
            log.info("No sales data found for items with rebate rules in date range");
            return Collections.emptyList();
        }

        // Fetch item details for description
        List<UUID> itemUuids = itemIds.stream()
                .map(UUID::fromString)
                .collect(Collectors.toList());
        Map<UUID, Item> itemMap = itemRepository.findAllByIdIn(itemUuids).stream()
                .collect(Collectors.toMap(Item::getId, item -> item));

        // Group rebate rules by item ID
        Map<UUID, List<ItemVendorRebateDto>> rebatesByItem = itemVendorRebateDtos.stream()
                .collect(Collectors.groupingBy(ItemVendorRebateDto::getItemId));

        // Process sales data and calculate rebates
        Map<String, RebateAggregation> aggregationMap = new HashMap<>();

        for (ItemSalesTrend sale : salesData) {
            UUID itemId = UUID.fromString(sale.getItemId());
            LocalDate saleDate = sale.getTimeDim().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            // Find applicable rebate rule for this sale date
            BigDecimal applicableRebatePerUnit = findApplicableRebate(rebatesByItem.get(itemId), saleDate);

            // Only process sales with applicable rebates (including $0 rebates for completeness)
            BigDecimal totalRebateForSale = applicableRebatePerUnit.multiply(BigDecimal.valueOf(sale.getSalesQuantity()));

            // Aggregate by SKU
            String sku = sale.getSkuNumber();
            RebateAggregation aggregation = aggregationMap.computeIfAbsent(sku, k -> new RebateAggregation());
            aggregation.addSale(sale.getSalesQuantity(), totalRebateForSale);
            aggregation.setItemId(itemId);
        }

        // Create report data from aggregations
        return aggregationMap.entrySet().stream()
                .map(entry -> createRebateReportData(entry.getKey(), entry.getValue(), vendor, itemMap))
                .sorted(Comparator.comparing(RebateReportData::getSku))
                .collect(Collectors.toList());
    }

    /**
     * Find the applicable rebate rate for a specific sale date
     * If multiple rebate rules overlap, use the one with the latest start date
     */
    private BigDecimal findApplicableRebate(List<ItemVendorRebateDto> rebateRules, LocalDate saleDate) {
        if (rebateRules == null || rebateRules.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return rebateRules.stream()
                .filter(rebate -> isRebateApplicable(rebate, saleDate))
                .max(Comparator.comparing(ItemVendorRebateDto::getStartDate))
                .map(ItemVendorRebateDto::getRebatePerUnit)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Check if a rebate rule is applicable for a specific date
     */
    private boolean isRebateApplicable(ItemVendorRebateDto rebate, LocalDate saleDate) {
        boolean afterStart = !saleDate.isBefore(rebate.getStartDate());
        boolean beforeEnd = rebate.getEndDate() == null || !saleDate.isAfter(rebate.getEndDate());
        return afterStart && beforeEnd;
    }

    /**
     * Create RebateReportData from aggregation
     */
    private RebateReportData createRebateReportData(String sku, RebateAggregation aggregation, Vendor vendor, Map<UUID, Item> itemMap) {
        RebateReportData reportData = new RebateReportData();
        reportData.setSupplierName(vendor.getVendorName());
        reportData.setSku(sku);
        reportData.setUnitsSold(aggregation.getTotalUnits());
        reportData.setTotalRebate(aggregation.getTotalRebate());

        // Set per case rebate (weighted average)
        if (aggregation.getTotalUnits() > 0) {
            reportData.setPerCaseRebate(aggregation.getTotalRebate().divide(
                    BigDecimal.valueOf(aggregation.getTotalUnits()), 2, java.math.RoundingMode.HALF_UP));
        } else {
            reportData.setPerCaseRebate(BigDecimal.ZERO);
        }

        // Set description from item
        Item item = itemMap.get(aggregation.getItemId());
        if (item != null) {
            reportData.setDescription(item.getTitle() != null ? item.getTitle() : item.getName());
        } else {
            reportData.setDescription("Unknown Item");
        }

        return reportData;
    }

    /**
     * Helper class for aggregating rebate calculations by SKU
     */
    private static class RebateAggregation {
        private int totalUnits = 0;
        private BigDecimal totalRebate = BigDecimal.ZERO;
        private UUID itemId;

        public void addSale(int units, BigDecimal rebate) {
            this.totalUnits += units;
            this.totalRebate = this.totalRebate.add(rebate);
        }

        public int getTotalUnits() { return totalUnits; }
        public BigDecimal getTotalRebate() { return totalRebate; }
        public UUID getItemId() { return itemId; }
        public void setItemId(UUID itemId) { this.itemId = itemId; }
    }
}
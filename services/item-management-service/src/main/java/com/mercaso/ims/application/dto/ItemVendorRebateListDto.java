package com.mercaso.ims.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemVendorRebateListDto extends BaseDto implements PageableResult<ItemVendorRebateDto> {
    private List<ItemVendorRebateDto> data;
    private Long totalCount;
}

package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.ims.application.dto.*;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.query.ItemVendorRebateQuery;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

import static com.mercaso.ims.application.query.ItemQuery.getCustomFilterValue;

@Slf4j
public abstract class AbstractBulkExportStrategy implements BulkExportStrategy {

    private final ItemQueryApplicationService itemQueryApplicationService;
    private final Executor taskExecutor;
    private final ItemSearchApplicationService itemSearchApplicationService;
    private final ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService;

    protected AbstractBulkExportStrategy(
            ItemQueryApplicationService itemQueryApplicationService,
            Executor taskExecutor,
            ItemSearchApplicationService itemSearchApplicationService,
            ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService) {
        this.itemQueryApplicationService = itemQueryApplicationService;
        this.taskExecutor = taskExecutor;
        this.itemSearchApplicationService = itemSearchApplicationService;
        this.itemVendorRebateSearchApplicationService = itemVendorRebateSearchApplicationService;
    }

    protected List<ItemSerachDto> fetchItemsWithSimpleData(String customFilter) {
        return fetchItemsWithPagination(customFilter, this::processPageForSimpleData);
    }

    protected List<ItemSerachDto> fetchFullItemData(String customFilter) {
        return fetchItemsWithPagination(customFilter, this::processPageForFullData);
    }

    protected List<ItemVendorRebateDto> fetchItemVendorRebateData(String customFilter) {
        return fetchItemsWithPagination(customFilter, this::processPageForItemVendorRebateData);
    }


    private  <T> List<T> fetchItemsWithPagination(String customFilter,
                                                  PageProcessor<T> processor) {
        int pageSize = 2000;
        AtomicInteger pageNumber = new AtomicInteger(1);
        AtomicBoolean isLastPage = new AtomicBoolean(false);

        return StreamSupport.stream(
                Spliterators.spliteratorUnknownSize(new Iterator<List<T>>() {
                    @Override
                    public boolean hasNext() {
                        return !isLastPage.get();
                    }

                    @Override
                    public List<T> next() {
                        if (!hasNext()) {
                            throw new NoSuchElementException();
                        }

                        PageResult<T> result = processor.process(customFilter, pageNumber.get(), pageSize);

                        if (result.items.size() < pageSize) {
                            isLastPage.set(true);
                        }
                        pageNumber.incrementAndGet();
                        return result.items;
                    }
                }, Spliterator.ORDERED), true)
                .flatMap(List::stream)
                .toList();
    }


    protected <T, Q, R extends PageableResult<T>> List<T> fetchDataWithPagination(
            String customFilter,
            QueryBuilder<Q> queryBuilder,
            SearchService<Q, R> searchService) {

        int pageSize = 2000;
        AtomicInteger pageNumber = new AtomicInteger(1);
        AtomicBoolean isLastPage = new AtomicBoolean(false);

        return StreamSupport.stream(
                Spliterators.spliteratorUnknownSize(new Iterator<List<T>>() {
                    @Override
                    public boolean hasNext() {
                        return !isLastPage.get();
                    }

                    @Override
                    public List<T> next() {
                        if (!hasNext()) {
                            throw new NoSuchElementException();
                        }

                        Q query = queryBuilder.build(customFilter, pageNumber.get(), pageSize);
                        R result = searchService.search(query);

                        if (result.getData().size() < pageSize) {
                            isLastPage.set(true);
                        }
                        pageNumber.incrementAndGet();
                        return result.getData();
                    }
                }, Spliterator.ORDERED), true)
                .flatMap(List::stream)
                .toList();
    }



    private PageResult<ItemSerachDto> processPageForSimpleData(String customFilter, int pageNumber, int pageSize) {

        Map<String, String> customFilterValue = getCustomFilterValue(customFilter);
        ItemQuery itemQuery = ItemQuery.builder()
                .page(pageNumber)
                .pageSize(pageSize)
                .customFilter(customFilterValue)
                .build();

        List<UUID> itemIds = itemSearchApplicationService.searchItemListIds(itemQuery);
        if (itemIds.isEmpty()) {
            return new PageResult<>(List.of());
        }

        int subPageSize = 200;
        int totalSize = itemIds.size();

        int batches = (int) Math.ceil((double) totalSize / subPageSize);

        List<CompletableFuture<List<ItemDto>>> futures = IntStream.range(0, batches)
                .mapToObj(i -> {
                    int start = i * subPageSize;
                    int end = Math.min(start + subPageSize, totalSize);
                    List<UUID> subPageItemIds = itemIds.subList(start, end);

                    return CompletableFuture.supplyAsync(() ->
                            itemQueryApplicationService.findByIdIn(subPageItemIds), taskExecutor);

                }).toList();

        List<ItemDto> allItemDtos = futures.stream()
                .map(future -> {
                    try {
                        return future.join();
                    } catch (Exception e) {
                        log.error("Error retrieving batch in getItemsByPage", e);
                        return Collections.<ItemDto>emptyList();
                    }
                })
                .flatMap(List::stream)
                .toList();

        List<ItemSerachDto> itemSerachDtos = allItemDtos.stream()
                .map(item -> {
                    ItemSerachDto supplierItem = new ItemSerachDto();
                    supplierItem.setId(item.getId());
                    supplierItem.setSkuNumber(item.getSkuNumber());
                    supplierItem.setPrimaryVendorId(item.getPrimaryVendorId());
                    supplierItem.setBackupVendorId(item.getBackupVendorId());
                    return supplierItem;
                })
                .toList();

        return new PageResult<>(itemSerachDtos);
    }

    private PageResult<ItemSerachDto> processPageForFullData(String customFilter, int pageNumber, int pageSize) {
        Map<String, String> customFilterValue = getCustomFilterValue(customFilter);
        ItemQuery itemQuery = ItemQuery.builder()
                .page(pageNumber)
                .pageSize(pageSize)
                .customFilter(customFilterValue)
                .build();

        ItemListDto itemListDto = itemSearchApplicationService.searchItemListV2(itemQuery);
        return new PageResult<>(itemListDto.getData());
    }

    private PageResult<ItemVendorRebateDto> processPageForItemVendorRebateData(String customFilter, int pageNumber, int pageSize) {
        Map<String, String> customFilterValue = getCustomFilterValue(customFilter);
        ItemVendorRebateQuery itemVendorRebateQuery = ItemVendorRebateQuery.builder()
                .page(pageNumber)
                .pageSize(pageSize)
                .customFilter(customFilterValue)
                .build();

        ItemVendorRebateListDto itemVendorRebateListDto = itemVendorRebateSearchApplicationService.findByVendorIdAndDateRange(itemVendorRebateQuery);
        return new PageResult<>(itemVendorRebateListDto.getData());
    }

    @FunctionalInterface
    private interface PageProcessor<T> {
        PageResult<T> process(String customFilter, int pageNumber, int pageSize);
    }

    @FunctionalInterface
    protected interface QueryBuilder<Q> {
        Q build(String customFilter, int pageNumber, int pageSize);
    }

    @FunctionalInterface
    protected interface SearchService<Q, R> {
        R search(Q query);
    }

    private static class PageResult<T> {
        final List<T> items;

        PageResult(List<T> items) {
            this.items = items;
        }
    }
}

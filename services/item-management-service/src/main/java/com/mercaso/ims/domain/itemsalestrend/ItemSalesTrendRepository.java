package com.mercaso.ims.domain.itemsalestrend;

import com.mercaso.ims.domain.BaseDomainRepository;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface ItemSalesTrendRepository extends BaseDomainRepository<ItemSalesTrend, UUID> {

    List<ItemSalesTrend> findByItemIdAndTimeGrain(String itemId, ItemSalesTrendTimeGrain timeGrain);

    List<ItemSalesTrend> findByItemIdsAndTimeDimBetween(List<String> itemIds, Date startDate, Date endDate, ItemSalesTrendTimeGrain timeGrain);

}

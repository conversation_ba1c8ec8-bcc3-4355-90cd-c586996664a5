package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
public class SupplierBulkExportStrategy extends AbstractBulkExportStrategy {

    private final VendorService vendorService;
    private final BulkExportExcelGenerator excelGenerator;

    public SupplierBulkExportStrategy(ItemQueryApplicationService itemQueryApplicationService, Executor taskExecutor,
                                      ItemSearchApplicationService itemSearchApplicationService, VendorService vendorService,
                                      BulkExportExcelGenerator excelGenerator,
                                      ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService) {
        super(itemQueryApplicationService, taskExecutor, itemSearchApplicationService, itemVendorRebateSearchApplicationService);
        this.vendorService = vendorService;
        this.excelGenerator = excelGenerator;
    }

    @Override
    public ExportType getExportType() {
        return ExportType.SUPPLIER;
    }

    @Override
    public byte[] execute(String customFilter) {
        List<ItemSerachDto> items = fetchItemsWithSimpleData(customFilter);
        Map<UUID, Vendor> vendorMap = vendorService.findAll().stream()
                .collect(Collectors.toMap(Vendor::getId, v -> v));

        return excelGenerator.generateBulkExportReport(items, Collections.emptyList(), vendorMap, getExportType());
    }
}
package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;
import org.springframework.stereotype.Component;

@Component
public class ItemBulkExportStrategy extends AbstractBulkExportStrategy {

    private final FinaleAdaptor finaleAdaptor;
    private final BulkExportExcelGenerator excelGenerator;

    public ItemBulkExportStrategy(ItemQueryApplicationService itemQueryApplicationService, Executor taskExecutor,
        ItemSearchApplicationService itemSearchApplicationService, FinaleAdaptor finaleAdaptor,
        BulkExportExcelGenerator excelGenerator, ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService) {

        super(itemQueryApplicationService, taskExecutor, itemSearchApplicationService, itemVendorRebateSearchApplicationService);
        this.finaleAdaptor = finaleAdaptor;
        this.excelGenerator = excelGenerator;
    }

    @Override
    public ExportType getExportType() {
        return ExportType.ITEM;
    }

    @Override
    public byte[] execute(String customFilter) {
        List<ItemSerachDto> items = fetchFullItemData(customFilter);
        List<FinaleAvailableStockDto> finaleData = finaleAdaptor.getAllProducts();
        return excelGenerator.generateBulkExportReport(items, finaleData, Collections.emptyMap(), getExportType());
    }
}

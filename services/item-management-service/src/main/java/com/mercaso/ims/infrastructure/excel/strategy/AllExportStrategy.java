package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class AllExportStrategy extends AbstractBulkExportStrategy {

    private final FinaleAdaptor finaleAdaptor;
    private final VendorService vendorService;
    private final BulkExportExcelGenerator excelGenerator;

    public AllExportStrategy(
            ItemQueryApplicationService itemQueryApplicationService,
            Executor taskExecutor,
            ItemSearchApplicationService itemSearchApplicationService,
            FinaleAdaptor finaleAdaptor,
            VendorService vendorService,
            BulkExportExcelGenerator excelGenerator,
            ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService) {
        super(itemQueryApplicationService, taskExecutor, itemSearchApplicationService, itemVendorRebateSearchApplicationService);
        this.finaleAdaptor = finaleAdaptor;
        this.vendorService = vendorService;
        this.excelGenerator = excelGenerator;
    }

    @Override
    public ExportType getExportType() {
        return ExportType.ALL;
    }

    @Override
    public byte[] execute(String customFilter) {
        List<ItemSerachDto> items = fetchFullItemData(customFilter);

        List<FinaleAvailableStockDto> finaleData = finaleAdaptor.getAllProducts();

        Map<UUID, Vendor> vendorMap = vendorService.findAll().stream()
                .collect(Collectors.toMap(Vendor::getId, v -> v));

        return excelGenerator.generateBulkExportReport(items, finaleData, vendorMap, getExportType());
    }
}
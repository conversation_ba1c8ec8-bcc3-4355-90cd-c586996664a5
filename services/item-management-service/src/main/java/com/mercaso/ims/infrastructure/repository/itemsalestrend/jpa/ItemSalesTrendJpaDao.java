package com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa;

import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa.dataobject.ItemSalesTrendDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface ItemSalesTrendJpaDao extends JpaRepository<ItemSalesTrendDo, UUID> {

    List<ItemSalesTrendDo> findByItemIdAndTimeGrain(String itemId, ItemSalesTrendTimeGrain timeGrain);

    List<ItemSalesTrendDo> findByItemIdInAndTimeDimBetweenAndTimeGrain(List<String> itemIds, Date startDate, Date endDate, ItemSalesTrendTimeGrain timeGrain);

}
